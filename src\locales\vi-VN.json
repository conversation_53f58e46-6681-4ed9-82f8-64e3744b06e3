{"title": "<PERSON><PERSON><PERSON><PERSON>", "theme": "Chủ Đề", "openSource": "<PERSON><PERSON> Mở", "copyright": "Tạo với ❤️ bởi nhóm {{name}}", "searchPlaceholder": "<PERSON><PERSON> lòng nhập từ khóa", "research": {"common": {"startThinking": "<PERSON><PERSON><PERSON> đ<PERSON>u suy nghĩ", "rethinking": "<PERSON><PERSON> <PERSON> lại", "thinkingQuestion": "<PERSON><PERSON> suy nghĩ câu hỏi...", "writeReportPlan": "<PERSON><PERSON><PERSON><PERSON> kế hoạch báo cáo", "rewriteReportPlan": "<PERSON><PERSON><PERSON><PERSON> lại kế hoạch báo cáo", "startResearch": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> ng<PERSON> cứu", "restartResearch": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> l<PERSON>i", "writeReport": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> c<PERSON>o", "continueResearch": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> ng<PERSON>", "indepthResearch": "<PERSON><PERSON><PERSON><PERSON> c<PERSON><PERSON> sâu h<PERSON>n", "rewriteReport": "<PERSON><PERSON><PERSON><PERSON> lại b<PERSON>o c<PERSON>o", "sources": "<PERSON><PERSON><PERSON><PERSON>", "thinking": "<PERSON><PERSON> suy nghĩ...", "research": "<PERSON><PERSON> ng<PERSON> c<PERSON>...", "writing": "Đang viết...", "newResearch": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> mới", "addToKnowledgeBase": "<PERSON><PERSON><PERSON><PERSON> vào cơ sở tri thức", "addToKnowledgeBaseTip": "<PERSON><PERSON> thêm vào cơ sở tri thức", "restudy": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> l<PERSON>i", "edit": "Chỉnh sửa", "save": "<PERSON><PERSON><PERSON>", "copy": "Sao chép", "export": "<PERSON><PERSON><PERSON>", "delete": "Xóa"}, "topic": {"title": "1. <PERSON><PERSON> <PERSON>", "topicLabel": "1.1 <PERSON><PERSON> đ<PERSON> nghiên cứu", "topicPlaceholder": "B<PERSON>t kỳ câu hỏi nào bạn muốn biết..."}, "feedback": {"title": "2. Đặt <PERSON><PERSON>u Hỏi", "emptyTip": "<PERSON><PERSON> chờ chủ đề nghiên cứu...", "feedbackLabel": "<PERSON><PERSON><PERSON> tr<PERSON> lời của bạn (không bắt buộc)", "feedbackPlaceholder": "Bạn có thể trả lời bất kỳ điều gì bạn muốn...", "questions": "2.1 Câu hỏi hệ thống", "reportPlan": "2.2 <PERSON><PERSON> ho<PERSON>ch b<PERSON><PERSON> c<PERSON><PERSON> ng<PERSON> c<PERSON>u"}, "searchResult": {"title": "3. <PERSON><PERSON><PERSON>", "emptyTip": "<PERSON><PERSON> chờ nhiệm vụ nghiên cứu...", "suggestionLabel": "<PERSON><PERSON><PERSON> <PERSON> nghiên cứu (kh<PERSON><PERSON> b<PERSON><PERSON> buộc)", "suggestionPlaceholder": "<PERSON><PERSON> muốn thêm hoặc điều chỉnh hướng nghiên cứu không...", "references": "<PERSON><PERSON><PERSON> li<PERSON>u tham kh<PERSON>o", "relatedImages": "<PERSON><PERSON><PERSON>nh liên quan"}, "finalReport": {"title": "4. <PERSON><PERSON><PERSON>", "emptyTip": "<PERSON><PERSON> chờ tổng hợp dữ liệu...", "researchedInfor": "<PERSON><PERSON> nghiên c<PERSON> {{total}} trang web", "localResearchedInfor": "<PERSON><PERSON> nghiên cứu {{total}} tài nguyên cục bộ", "writingRequirementLabel": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> v<PERSON> (k<PERSON><PERSON><PERSON> b<PERSON><PERSON> buộc)", "writingRequirementPlaceholder": "<PERSON><PERSON>n có thể nêu bất kỳ yêu cầu nào liên quan đến việc viết báo cáo."}}, "history": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> sử nghiên cứu được lưu trữ cục bộ trong trình duyệt và chỉ lưu các nghiên cứu đã hoàn thành.", "name": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "emptyTip": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> bản ghi lịch sử", "date": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "import": "<PERSON><PERSON><PERSON><PERSON>", "importTip": "<PERSON><PERSON><PERSON><PERSON>", "importSuccess": "{{title}} đã nhập thành công.", "importFailed": "{{title}} nh<PERSON>p thất bại.", "load": "<PERSON><PERSON><PERSON>", "export": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "loadMore": "<PERSON><PERSON><PERSON> thêm l<PERSON>ch sử", "close": "Đ<PERSON><PERSON>", "noHistory": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> bản ghi lịch sử"}, "knowledge": {"title": "Cơ Sở <PERSON>", "description": "C<PERSON> sở tri thức đư<PERSON><PERSON> lưu trữ cục bộ trong trình du<PERSON>.", "create": "<PERSON><PERSON><PERSON> mới", "createTip": "<PERSON><PERSON><PERSON> tri thức mới", "emptyTip": "<PERSON><PERSON><PERSON><PERSON> có nội dung", "name": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "action": "<PERSON><PERSON><PERSON> đ<PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa", "delete": "Xóa", "loadMore": "<PERSON><PERSON><PERSON> thêm tri thức", "resource": "<PERSON><PERSON><PERSON>", "fileInfor": "<PERSON><PERSON><PERSON> lên bởi người dùng lúc {{createdAt}}.", "urlInfor": "<PERSON><PERSON><PERSON> từ người dùng lúc {{createdAt}}.", "createInfor": "Tạ<PERSON> bởi người dùng lúc {{createdAt}}.", "webCrawler": "<PERSON>hu thập web", "webCrawlerTip": "<PERSON><PERSON><PERSON><PERSON> thu thập web lấy nội dung trang từ URL chỉ định và trả về dữ liệu dạng Markdown.", "urlPlaceholder": "<PERSON><PERSON> lòng nhập URL...", "urlError": "<PERSON><PERSON> lòng nhập URL hợp lệ", "localCrawler": "<PERSON><PERSON> thập c<PERSON> bộ", "clear": "Xóa", "fetch": "<PERSON><PERSON><PERSON> d<PERSON> liệu", "localResourceTitle": "1.2 <PERSON><PERSON><PERSON> nguyên nghiên c<PERSON> cụ<PERSON> bộ (không bắt buộc)", "addResource": "<PERSON><PERSON><PERSON><PERSON> tài nguyên", "addResourceMessage": "{{title}} đã đ<PERSON><PERSON><PERSON> thêm vào tài nguyên.", "resourceNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài nguyên", "knowledge": "<PERSON> thức", "localFile": "<PERSON><PERSON><PERSON> c<PERSON> bộ", "webPage": "Trang web", "editor": {"title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "titlePlaceholder": "<PERSON><PERSON> lòng nhập tiêu đề...", "content": "<PERSON><PERSON><PERSON> dung (Markdown)", "back": "Quay lại", "reset": "Đặt lại", "submit": "<PERSON><PERSON><PERSON>"}}, "artifact": {"AIWrite": "AI Viết", "writingPromptTip": "<PERSON><PERSON> lòng nhập yêu cầu viết...", "readingLevel": "<PERSON><PERSON><PERSON><PERSON> độ đọc", "PhD": "<PERSON><PERSON><PERSON><PERSON> sĩ", "college": "<PERSON><PERSON><PERSON>", "teenager": "<PERSON><PERSON><PERSON><PERSON>", "child": "Trẻ em", "pirate": "<PERSON><PERSON><PERSON><PERSON>", "adjustLength": "<PERSON>iều chỉnh độ dài", "longest": "<PERSON><PERSON><PERSON>", "long": "<PERSON><PERSON><PERSON>", "shorter": "<PERSON><PERSON><PERSON>", "shortest": "<PERSON><PERSON><PERSON>", "translate": "<PERSON><PERSON><PERSON>", "continuation": "<PERSON><PERSON><PERSON><PERSON>", "addEmojis": "<PERSON><PERSON><PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON>"}, "knowledgeGraph": {"action": "<PERSON><PERSON><PERSON> sơ đồ tri thức", "regenerate": "<PERSON><PERSON><PERSON> lạ<PERSON>", "edit": "Chỉnh sửa", "view": "Xem"}, "editor": {"copy": "Sao chép", "mermaid": {"downloadSvg": "<PERSON><PERSON><PERSON> xuống dưới dạng hình <PERSON>nh", "copyText": "<PERSON><PERSON> ch<PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "zoomIn": "<PERSON><PERSON><PERSON> to", "zoomOut": "<PERSON>hu nhỏ", "resize": "<PERSON><PERSON> đ<PERSON>i kích th<PERSON>"}, "tooltip": {"bold": "In đậm", "italic": "In nghiêng", "strikethrough": "<PERSON><PERSON><PERSON> ng<PERSON>", "code": "Mã", "math": "<PERSON><PERSON><PERSON> thức", "link": "<PERSON><PERSON><PERSON>", "quote": "<PERSON><PERSON><PERSON><PERSON> dẫn"}, "slash": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "description": "<PERSON><PERSON><PERSON> tiêu đ<PERSON>"}, "h1": {"name": "Tiêu đề 1", "description": "<PERSON><PERSON><PERSON> tiêu đ<PERSON> 1"}, "h2": {"name": "Tiêu đề 2", "description": "<PERSON><PERSON><PERSON> ti<PERSON>u đ<PERSON> 2"}, "h3": {"name": "Tiêu đề 3", "description": "<PERSON><PERSON><PERSON> ti<PERSON>u đ<PERSON> 3"}, "h4": {"name": "T<PERSON>êu <PERSON> 4", "description": "<PERSON><PERSON><PERSON> ti<PERSON>u đ<PERSON> 4"}, "h5": {"name": "<PERSON><PERSON><PERSON><PERSON> 5", "description": "<PERSON><PERSON><PERSON> ti<PERSON>u đ<PERSON> 5"}, "h6": {"name": "<PERSON><PERSON><PERSON><PERSON> 6", "description": "<PERSON><PERSON><PERSON> ti<PERSON>u đ<PERSON> 6"}, "list": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> da<PERSON>ch"}, "ul": {"name": "<PERSON><PERSON> s<PERSON>ch chấm", "description": "<PERSON><PERSON><PERSON> mục danh sách chấm"}, "ol": {"name": "<PERSON><PERSON> s<PERSON>ch s<PERSON>", "description": "<PERSON><PERSON><PERSON> m<PERSON>c danh s<PERSON>ch số"}, "todo": {"name": "<PERSON><PERSON> s<PERSON>ch v<PERSON><PERSON><PERSON> c<PERSON>n làm", "description": "<PERSON><PERSON><PERSON> mục danh sách vi<PERSON><PERSON> c<PERSON>n làm"}, "advanced": {"name": "<PERSON><PERSON><PERSON> cao", "description": "<PERSON><PERSON><PERSON> n<PERSON>g cao"}, "link": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> liên k<PERSON>t"}, "image": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>"}, "code": {"name": "<PERSON><PERSON><PERSON><PERSON> mã", "description": "<PERSON><PERSON><PERSON> k<PERSON>i mã"}, "math": {"name": "<PERSON><PERSON><PERSON><PERSON> công thức", "description": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>i công thức"}, "table": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> b<PERSON> 3x3"}, "quote": {"name": "<PERSON><PERSON><PERSON><PERSON> dẫn", "description": "<PERSON><PERSON><PERSON> trích dẫn"}, "horizontal": {"name": "<PERSON><PERSON><PERSON><PERSON> ngang", "description": "<PERSON><PERSON><PERSON> đường kẻ ngang"}}, "placeholder": "<PERSON><PERSON> lòng nhập văn bản, hoặc nhập \"/\" để sử dụng lệnh"}, "setting": {"title": "Cài Đặt", "description": "Tất cả cài đặt được lưu trong trình duyệt của người dùng.", "model": "<PERSON><PERSON>", "general": "<PERSON><PERSON>", "provider": "<PERSON>hà cung cấp AI", "providerTip": "Dịch vụ AI tổng hợp như One API hoặc New API nên chọn `Tương thích OpenAI`.", "openAICompatible": "Tương thích OpenAI", "free": "<PERSON><PERSON><PERSON> phí", "mode": "Chế Độ API", "modeTip": "Trong chế độ cục bộ, tất cả yêu cầu đượ<PERSON> gửi trực tiếp bởi trình duyệt. Trong chế độ proxy, tất cả yêu cầu đi qua máy chủ.", "local": "<PERSON><PERSON><PERSON> bộ", "proxy": "Proxy", "apiKeyLabel": "Khóa API", "apiKeyPlaceholder": "<PERSON><PERSON> lòng nhập khóa API", "apiUrlLabel": "URL Cơ Sở API", "apiUrlPlaceholder": "<PERSON><PERSON> lò<PERSON> nhập URL cơ sở của API", "resourceNameLabel": "<PERSON><PERSON><PERSON>", "resourceNamePlaceholder": "<PERSON><PERSON> lòng nhập tên tài nguyên", "apiVersionLabel": "<PERSON><PERSON><PERSON> b<PERSON>", "apiVersionPlaceholder": "<PERSON><PERSON> lòng nh<PERSON><PERSON> p<PERSON>ên bản <PERSON>", "accessPassword": "<PERSON><PERSON><PERSON> truy cập", "accessPasswordPlaceholder": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u truy cập máy chủ", "accessPasswordTip": "Xác thực API phía máy chủ giúp ngăn API bị lạm dụng từ ứng dụng ngoài.", "thinkingModel": "<PERSON><PERSON> hình suy nghĩ", "thinkingModelTip": "<PERSON><PERSON> hình chính dùng cho nghiên cứu sâu, nên chọn mô hình chuyên về suy nghĩ.", "networkingModel": "<PERSON><PERSON> n<PERSON> vụ", "networkingModelTip": "<PERSON>ô hình dùng cho nhiệm vụ phụ, nên chọn mô hình có đầu ra cao.", "recommendedModels": "<PERSON><PERSON> hình đư<PERSON><PERSON> đề xuất", "basicModels": "<PERSON><PERSON> h<PERSON> c<PERSON> bản", "modelListLoadingPlaceholder": "<PERSON><PERSON> lòng chọn mô hình", "modelListPlaceholder": "<PERSON><PERSON> lòng nhập tên mô hình", "refresh": "<PERSON><PERSON><PERSON> mới danh sách mô hình", "modelListLoading": "<PERSON><PERSON> tải danh sách mô hình", "search": "<PERSON><PERSON><PERSON>", "webSearch": "<PERSON><PERSON><PERSON> k<PERSON> web", "webSearchTip": "<PERSON><PERSON><PERSON> kiếm web gi<PERSON>p lấy dữ liệu mới và giảm ảo tưởng của mô hình. <PERSON><PERSON><PERSON> bật.", "enable": "<PERSON><PERSON><PERSON>", "disable": "Tắt", "searchProvider": "<PERSON><PERSON><PERSON> cung cấp tìm kiếm", "searchProviderTip": "Một số mô hình tích hợp sẵn tìm kiếm web, còn lại cần bên thứ ba hỗ trợ.", "modelBuiltin": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> mô hình", "bocha": "<PERSON><PERSON>", "parallelSearch": "<PERSON><PERSON><PERSON> k<PERSON> song song", "parallelSearchTip": "<PERSON><PERSON><PERSON> tốc thu thập dữ liệu, nh<PERSON><PERSON> có thể bị giới hạn số yêu cầu/phút.", "searchResults": "<PERSON><PERSON><PERSON> qu<PERSON> tìm kiếm", "searchResultsTip": "<PERSON><PERSON> lượng kết quả tìm kiếm tối đa.", "searchApiKeyPlaceholder": "<PERSON><PERSON> lòng nhập khóa API tìm kiếm", "searchScope": "Phạm vi tìm kiếm", "scopeValue": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "academic": "<PERSON><PERSON><PERSON>", "general": "<PERSON><PERSON><PERSON> quát", "news": "<PERSON> tức", "researchPaper": "<PERSON><PERSON><PERSON>", "financial": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>ng ty", "personalSite": "Trang cá nhân", "github": "<PERSON><PERSON><PERSON>", "linkedin": "LinkedIn", "pdf": "PDF"}, "language": "<PERSON><PERSON><PERSON>", "languageTip": "<PERSON><PERSON> thống sẽ tự chọn ngôn ngữ dựa theo trình <PERSON>.", "system": "<PERSON><PERSON> th<PERSON>", "light": "<PERSON><PERSON><PERSON>", "dark": "<PERSON><PERSON><PERSON>", "debug": "Gỡ lỗi", "debugTip": "<PERSON>ế độ gỡ lỗi gi<PERSON><PERSON> phát hiện lỗi yêu cầu. Thông thường không cần bật.", "PWA": "Cài đặt PWA", "PWATip": "Ứng dụng web hiện đại (PWA) mang lại trải nghiệm như ứng dụng gốc.", "installlPWA": "<PERSON><PERSON>i đặt ứng dụng trình <PERSON> (PWA)", "resetSetting": "Đặt lại cài đặt", "resetAllSettings": "Đặt lại và xóa bộ nhớ đệm", "resetSettingWarning": "<PERSON><PERSON> tác này sẽ xóa tất cả dữ liệu và khởi tạo lại dự án.", "version": "<PERSON><PERSON><PERSON> bản hiện tại", "checkForUpdate": "<PERSON><PERSON><PERSON> tra cập nh<PERSON>t", "experimental": "<PERSON><PERSON><PERSON>", "references": "<PERSON><PERSON><PERSON> li<PERSON>u tham kh<PERSON>o", "referencesTip": "Thê<PERSON> liên kết tham khảo vào kết quả và báo cáo. <PERSON><PERSON> thể không hỗ trợ tốt với mô hình nhỏ.", "citationImage": "<PERSON><PERSON>nh <PERSON>nh trích dẫn", "citationImageTip": "<PERSON><PERSON><PERSON> hình <PERSON>nh tham khảo để chèn hình minh họa vào báo cáo.", "textOutputMode": "<PERSON><PERSON> độ xuất văn bản", "textOutputModeTip": "<PERSON><PERSON>u ứng xuất văn bản của AI. <PERSON><PERSON> tự, xuất văn bản giống như máy đánh chữ. <PERSON><PERSON>, xu<PERSON><PERSON> the<PERSON> từ, đầu ra hiệu quả nhất. <PERSON><PERSON><PERSON>, hiệu <PERSON>ng đầu ra ổn định nhất.", "character": "<PERSON><PERSON>", "word": "Từ", "line": "Dòng", "save": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "useLocalResource": "Chỉ sử dụng tài nguyên cục bộ", "useLocalResourceTip": "<PERSON><PERSON><PERSON>, chỉ sử dụng tài nguyên cục bộ cho nghiên cứu và thu thập dữ liệu web. Không gửi yêu cầu ra ngoài."}}