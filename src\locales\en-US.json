{"title": "Deep Research", "theme": "Theme", "openSource": "Open Source", "copyright": "Created with ❤️ by {{name}} team", "searchPlaceholder": "Please enter keywords", "research": {"common": {"startThinking": "Start Thinking", "rethinking": "Rethinking", "thinkingQuestion": "Thinking Question...", "writeReportPlan": "Write Report Plan", "rewriteReportPlan": "Rewrite Report Plan", "startResearch": "Start Research", "restartResearch": "Restart Research", "writeReport": "Write Report", "continueResearch": "Continue Research", "indepthResearch": "Further Research", "rewriteReport": "Rewrite Report", "sources": "Sources", "thinking": "Thinking...", "research": "Research...", "writing": "Writing...", "newResearch": "New Research", "addToKnowledgeBase": "Add to Knowledge Base", "addToKnowledgeBaseTip": "Added to Knowledge Base", "restudy": "Re-study", "edit": "Edit", "save": "Save", "copy": "Copy", "export": "Export", "delete": "Delete"}, "topic": {"title": "1. Research Topics", "topicLabel": "1.1 Research topics", "topicPlaceholder": "Any questions you want to know..."}, "feedback": {"title": "2. Ask Question", "emptyTip": "Waiting for research topic...", "feedbackLabel": "Your Answer (Optional)", "feedbackPlaceholder": "You can answer whatever you want...", "questions": "2.1 System Questions", "reportPlan": "2.2 Research report plan"}, "searchResult": {"title": "3. Information Collection", "emptyTip": "Waiting for research task...", "suggestionLabel": "Research suggestions (Optional)", "suggestionPlaceholder": "Whether to add or adjust research directions...", "references": "References", "relatedImages": "Related Images"}, "finalReport": {"title": "4. Final Report", "emptyTip": "Waiting for data to be collated...", "researchedInfor": "Researched {{total}} websites", "localResearchedInfor": "Researched {{total}} local resources", "writingRequirementLabel": "Writing requirements (optional)", "writingRequirementPlaceholder": "You can raise any request related to report writing."}}, "history": {"title": "History", "description": "Research history is stored locally in the browser, and only save completed research.", "name": "Title", "emptyTip": "No history records", "date": "Date", "actions": "Action", "import": "Import", "importTip": "Import Research", "importSuccess": "{{title}} Imported successfully.", "importFailed": "{{title}} Import failed.", "load": "Load", "export": "Export", "delete": "Delete", "loadMore": "Load More History", "close": "Close", "noHistory": "No history records"}, "knowledge": {"title": "Local Knowledge Base", "description": "A knowledge base stored locally in the browser.", "create": "Create", "createTip": "Create new knowledge", "emptyTip": "No Content", "name": "Name", "size": "Size", "date": "Date", "action": "Action", "add": "Add", "edit": "Edit", "delete": "Delete", "loadMore": "Load More Knowledges", "resource": "Resource", "fileInfor": "Uploaded by user at {{createdAt}}.", "urlInfor": "Fetched by user at {{createdAt}}.", "createInfor": "Created by user at {{createdAt}}.", "webCrawler": "Web Crawler", "webCrawlerTip": "The web crawler obtains the page content of the specified URL through server and returns the data in Markdown format.", "urlPlaceholder": "Please enter the URL...", "urlError": "Please enter a valid URL", "localCrawler": "Local Crawler", "clear": "Clear", "fetch": "<PERSON>tch", "localResourceTitle": "1.2 Local research resources (optional)", "addResource": "Add Resource", "addResourceMessage": "{{title}} has been added to the resource.", "resourceNotFound": "Resource not found", "knowledge": "Knowledge", "localFile": "Local File", "webPage": "Web Page", "editor": {"title": "Title", "titlePlaceholder": "Please enter a title...", "content": "Content (Markdown)", "back": "Back", "reset": "Reset", "submit": "Submit"}}, "artifact": {"AIWrite": "AI Write", "writingPromptTip": "Please enter writing prompts...", "readingLevel": "Reading Level", "PhD": "PhD", "college": "College", "teenager": "Teenager", "child": "Child", "pirate": "Pirate", "adjustLength": "Adjust Length", "longest": "Longest", "long": "<PERSON>", "shorter": "Shorter", "shortest": "Shortest", "translate": "Translate", "continuation": "Continuation", "addEmojis": "Add <PERSON>s", "send": "Send"}, "knowledgeGraph": {"action": "Generate Knowledge Graph", "regenerate": "Regenerate", "edit": "Edit", "view": "View"}, "editor": {"copy": "Copy", "mermaid": {"downloadSvg": "Download as Image", "copyText": "Copy Text", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "resize": "Resize"}, "tooltip": {"bold": "Bold", "italic": "Italic", "strikethrough": "Strikethrough", "code": "Code", "math": "Math", "link": "Link", "quote": "Quote"}, "slash": {"heading": {"name": "Heading", "description": "Insert Heading"}, "h1": {"name": "Heading 1", "description": "Insert Heading 1"}, "h2": {"name": "Heading 2", "description": "Insert Heading 2"}, "h3": {"name": "Heading 3", "description": "Insert Heading 3"}, "h4": {"name": "Heading 4", "description": "Insert Heading 4"}, "h5": {"name": "Heading 5", "description": "Insert Heading 5"}, "h6": {"name": "Heading 6", "description": "Insert Heading 6"}, "list": {"name": "List", "description": "Insert List"}, "ul": {"name": "Bullet List", "description": "Insert Bullet List Item"}, "ol": {"name": "Ordered List", "description": "Insert Ordered List Item"}, "todo": {"name": "Todo List", "description": "Insert Todo List Item"}, "advanced": {"name": "Advanced", "description": "Advanced Commands"}, "link": {"name": "Link", "description": "Insert Link"}, "image": {"name": "Image", "description": "Insert Image"}, "code": {"name": "Code Block", "description": "Insert Code Block"}, "math": {"name": "Math Block", "description": "Insert Math Block"}, "table": {"name": "Table", "description": "Insert a 3x3 Table"}, "quote": {"name": "Quote", "description": "Insert Quote"}, "horizontal": {"name": "Horizontal", "description": "Insert Horizontal"}}, "placeholder": "Please enter text, or enter \"/ \" to use commands"}, "setting": {"title": "Setting", "description": "All settings are saved in the user's browser.", "model": "Model", "general": "System", "provider": "AI Provider", "providerTip": "AI service provider. For AI aggregation services such as One Api and New Api, please select `Compatible OpenAI`.", "openAICompatible": "OpenAI Compatible", "free": "Free", "mode": "API Mode", "modeTip": "In local mode, all requests are sent directly by the browser. In proxy mode, all requests are forwarded through the server.", "local": "Local", "proxy": "Proxy", "apiKeyLabel": "Api Key", "apiKeyPlaceholder": "Please enter the model API key", "apiUrlLabel": "Api Base Url", "apiUrlPlaceholder": "Please enter the API base URL", "resourceNameLabel": "Resource Name", "resourceNamePlaceholder": "Please enter the resource name", "apiVersionLabel": "Api Version", "apiVersionPlaceholder": "Please enter the API version", "accessPassword": "Access Password", "accessPasswordPlaceholder": "Please enter the server access password", "accessPasswordTip": "Server-side API authentication prevents API from being misused by external applications.", "thinkingModel": "Thinking Model", "thinkingModelTip": "The core model used in deep research, it is recommended to use the thinking model.", "networkingModel": "Task Model", "networkingModelTip": "The model used for secondary tasks, high output models are recommended.", "recommendedModels": "Recommended Models", "basicModels": "Basic Models", "modelListLoadingPlaceholder": "Please select model", "modelListPlaceholder": "Please enter the model name", "refresh": "Click Refresh Model", "modelListLoading": "Model list loading", "search": "Search", "webSearch": "Web Search", "webSearchTip": "Web search can obtain the latest data and help reduce model `hallucinations`. It is recommended to enable it.", "enable": "Enable", "disable": "Disable", "searchProvider": "Search Provider", "searchProviderTip": "Search service providers. Some models have built-in network search capabilities, while most models need to rely on third-party search engines to achieve networking capabilities.", "modelBuiltin": "Model built-in", "bocha": "<PERSON><PERSON>", "parallelSearch": "Parallel Search", "parallelSearchTip": "Parallel search helps speed up the data collection process, but too high a rate may trigger the model's request per minute limit.", "searchResults": "Search Results", "searchResultsTip": "The maximum number of web searches. Some search engines do not support this parameter.", "searchApiKeyPlaceholder": "Please enter your Api Key", "searchScope": "<PERSON> Scope", "scopeValue": {"all": "All", "academic": "Academic", "general": "General", "news": "News", "researchPaper": "Research Paper", "financial": "Financial", "company": "Company", "personalSite": "Personal Site", "github": "<PERSON><PERSON><PERSON>", "linkedin": "Linkedin", "pdf": "PDF"}, "language": "Language", "languageTip": "The system will automatically select the interface language based on the user's browser language.", "system": "System", "light": "Light", "dark": "Dark", "debug": "Debug", "debugTip": "System debugging can help users capture request exceptions and locate unknown errors. Normally, it does not need to be enabled.", "PWA": "Install PWA", "PWATip": "A Progressive Web App is an application built using web platform technologies that provides a user experience close to that of native applications.", "installlPWA": "Install browser application (PWA)", "resetSetting": "Reset Settings", "resetAllSettings": "Reset settings and clear the cache", "resetSettingWarning": "This operation will clear all data and initialize the project.", "version": "Current version", "checkForUpdate": "Check for update", "experimental": "Experimental", "references": "References", "referencesTip": "Enabling references will add reference links to search results and final reports via prompt. This may not work well for small models.", "citationImage": "Citation Image", "citationImageTip": "Enabling reference images will attempt to add content-related images to the final report through a prompt.", "textOutputMode": "Text Output Mode", "textOutputModeTip": "Text output effect of AI. Characters, output text like a typewriter. Words, output by word, the output is most efficient. Lines, the output effect is most stable.", "character": "Character", "word": "Word", "line": "Line", "save": "Save", "confirm": "Confirm", "cancel": "Cancel", "useLocalResource": "Use Local Resource Only", "useLocalResourceTip": "When enabled, only local resources will be used for research and web crawling. No external network requests will be made."}}